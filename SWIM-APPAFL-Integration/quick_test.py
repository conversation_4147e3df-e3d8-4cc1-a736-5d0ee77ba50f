# SWIM-APPAFL快速测试脚本
# 运行一个简化的测试来验证改进的稳定性
import subprocess
import sys
import os

def run_quick_test():
    """运行快速测试"""
    print("🚀 开始SWIM-APPAFL改进版本快速测试")
    print("=" * 60)
    
    # 测试命令（使用改进的参数）
    cmd = [
        sys.executable, 'main.py',
        '--dataset', 'cifar10',
        '--n_clients', '5',           # 减少客户端数量以加快测试
        '--cfraction', '0.8',         # 增加参与比例
        '--local_epochs', '2',        # 减少本地训练轮数
        '--comm_rounds', '10',        # 减少通信轮数
        '--use_swim', '1',
        '--model', 'resnet',
        '--model_version', 'optimized',
        '--train_batch_size', '32',   # 改进的批次大小
        '--temperature', '0.7',       # 改进的温度参数
        '--out_dim', '128',           # 改进的投影维度
        '--kr', '0.3',                # 改进的滑动窗口参数
        '--async_weight_strategy', 'fixed_schedule',  # 改进的权重策略
        '--lr', '0.01',
        '--seed', '42'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("预计运行时间: 5-10分钟")
    print("-" * 60)
    
    try:
        # 运行测试
        result = subprocess.run(cmd, check=True, text=True)
        
        print("\n" + "=" * 60)
        print("✅ 快速测试成功完成！")
        print("=" * 60)
        print("改进要点总结:")
        print("1. ✓ 添加了梯度裁剪机制")
        print("2. ✓ 改进了对比学习损失的数值稳定性")
        print("3. ✓ 优化了动态权重计算策略")
        print("4. ✓ 添加了学习率调度和预热机制")
        print("5. ✓ 改进了模型初始化和正则化")
        print("6. ✓ 集成了训练监控和调试机制")
        print("\n请查看生成的日志文件和训练曲线图以了解详细结果。")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 测试失败，返回码: {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⏹️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 测试出现异常: {e}")
        return False

def check_requirements():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查必要文件
    required_files = [
        'main.py',
        'clients.py', 
        'server.py',
        'swim_models.py',
        'training_monitor.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 检查Python包
    try:
        import torch
        import numpy as np
        import matplotlib.pyplot as plt
        print("✅ 运行环境检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少必要的Python包: {e}")
        return False

def main():
    """主函数"""
    print("SWIM-APPAFL改进版本快速测试")
    print("此脚本将运行一个简化的测试来验证改进效果")
    print()
    
    # 检查环境
    if not check_requirements():
        print("请先安装必要的依赖包或确保所有文件都存在")
        return
    
    # 运行测试
    success = run_quick_test()
    
    if success:
        print("\n🎉 测试完成！改进版本运行正常。")
        print("如需完整的对比测试，请运行: python test_improvements.py")
    else:
        print("\n💡 如果遇到问题，请检查:")
        print("1. GPU内存是否足够")
        print("2. 数据集是否正确下载")
        print("3. 依赖包是否正确安装")

if __name__ == '__main__':
    main()
