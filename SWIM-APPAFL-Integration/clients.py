# SWIM-APPAFL集成客户端实现
# 支持两种训练算法：1.APPAFL标准训练 2.SWIM对比学习训练
import numpy as np
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import math
import copy


class Client(object):
    """
    联邦学习客户端类
    支持APPAFL标准训练和SWIM对比学习训练两种模式
    """
    def __init__(self, train_dataloader, test_dataloader, device):
        """
        初始化客户端
        Args:
            train_dataloader: 训练数据加载器
            test_dataloader: 测试数据加载器  
            device: 计算设备（CPU或GPU）
        """
        self.train_dataloader = train_dataloader
        self.test_dataloader = test_dataloader
        self.device = device

    def localUpdate(self, model, epochs, lr, global_parameters, mu=0):
        """
        APPAFL标准本地训练方法
        Args:
            model: 神经网络模型
            epochs: 本地训练轮数
            lr: 学习率
            global_parameters: 全局模型参数
            mu: 正则化参数（用于FedProx等算法）
        Returns:
            model.state_dict(): 训练后的本地模型参数
        """
        # 加载全局模型参数到本地模型
        model.load_state_dict(global_parameters, strict=True)
        model.to(self.device)
        model.train()

        # 创建优化
        optimizer = torch.optim.SGD(model.parameters(), lr=lr,momentum=0.9)
        criterion = torch.nn.CrossEntropyLoss()

        # 进行本地训练
        for epoch in range(epochs):
            for batch_idx, (data, target) in enumerate(self.train_dataloader):
                # 将数据和标签移动到指定设备
                data, target = data.to(self.device), target.to(self.device)
                
                # 清零梯度
                optimizer.zero_grad()
                
                # 前向传播
                output = model(data)

                # 处理不同模型的输出格式
                if isinstance(output, tuple):
                    # SWIM模型返回 (features, projection, classification)
                    # 对于标准训练，我们只需要分类输出
                    _, _, classification = output
                    loss = criterion(classification, target)
                else:
                    # 标准模型直接返回分类结果
                    loss = criterion(output, target)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                optimizer.step()

        # 返回训练后的模型参数
        return model.state_dict()

    def localUpdateWithSWIM(self, model, epochs, lr, global_parameters,
                           global_model=None, previous_models=None,
                           mu=0.5, temperature=0.5, round_num=0, total_rounds=100, kr=0.5,
                           async_weight_strategy='local_rounds'):
        """
        SWIM对比学习本地训练方法（适配异步联邦学习）
        Args:
            model: 带投影头的神经网络模型
            epochs: 本地训练轮数
            lr: 学习率
            global_parameters: 全局模型参数
            global_model: 全局模型（用于对比学习）
            previous_models: 历史模型列表（用于对比学习）
            mu: 动态权重参数
            temperature: 对比学习温度参数
            round_num: 当前通信轮次
            total_rounds: 总通信轮次
            kr: 滑动窗口比例参数
            async_weight_strategy: 异步权重计算策略
                - 'local_rounds': 基于本地训练轮次
                - 'global_progress': 基于全局进度估计
                - 'fixed_schedule': 固定权重调度
        Returns:
            model.state_dict(): 训练后的本地模型参数
        """
        try:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 加载全局模型参数到本地模型
            model.load_state_dict(global_parameters, strict=True)
            model.to(self.device)
            model.train()

            # 创建优化器
            optimizer = torch.optim.SGD(model.parameters(), lr=lr, momentum=0.9, weight_decay=5e-4)
            criterion = torch.nn.CrossEntropyLoss()
            cos = torch.nn.CosineSimilarity(dim=-1)

            # 计算滑动窗口大小
            C = math.ceil(epochs * kr)
            if C == 0:
                C = 1
            Z_prev = [None] * C  # 存储历史投影表示
            cnt = 0

            # 确保全局模型在正确的设备上
            if global_model is not None:
                global_model = global_model.to(self.device)
                global_model.eval()

            # 确保历史模型在正确的设备上
            if previous_models is None:
                previous_models = []

            # 初始化客户端本地训练计数器（用于异步权重计算）
            if not hasattr(self, 'local_training_rounds'):
                self.local_training_rounds = 0

            # 进行本地训练
            for epoch in range(epochs):
                for batch_idx, (data, target) in enumerate(self.train_dataloader):
                    # 将数据和标签移动到指定设备
                    data, target = data.to(self.device), target.to(self.device)

                    # 清零梯度
                    optimizer.zero_grad()

                    # 前向传播：获取当前网络的特征、投影和分类输出
                    model_output = model(data)

                    # 确保模型输出是三元组（SWIM模型应该返回三个值）
                    if isinstance(model_output, tuple) and len(model_output) == 3:
                        features, projection, classification = model_output
                    else:
                        raise ValueError(f"SWIM模型应该返回三元组 (features, projection, classification)，但得到: {type(model_output)}")

                    # 调试信息：检查张量形状
                    if classification.dim() != 2:
                        print(f"警告：分类输出维度异常 - 形状: {classification.shape}, 目标形状: {target.shape}")
                        print(f"批次大小: {data.shape[0]}, 特征形状: {features.shape if features is not None else 'None'}")

                    # 计算分类损失
                    loss_classification = criterion(classification, target)

                    # 计算对比学习损失（遵循SWIM原始实现）
                    loss_contrastive = torch.tensor(0.0).to(self.device)

                    if global_model is not None:
                        with torch.no_grad():
                            # 获取全局模型的投影特征
                            _, global_projection, _ = global_model(data)

                        # 计算正样本相似度（当前模型与全局模型）- 使用全局平均池化确保维度一致
                        pooled_current = torch.mean(projection, dim=0, keepdim=True)  # (1, feature_dim)
                        pooled_global = torch.mean(global_projection, dim=0, keepdim=True)  # (1, feature_dim)
                        posi = torch.exp(torch.mean(cos(pooled_current, pooled_global).reshape(-1, 1)) / temperature)

                        # 初始化负样本相似度（遵循SWIM原始实现的方式）
                        nega = torch.exp(torch.mean(cos(pooled_current, pooled_current).reshape(-1, 1)) / temperature) - torch.exp(
                            torch.mean(cos(pooled_current, pooled_current).reshape(-1, 1)) / temperature)

                        # 处理历史模型的对比学习
                        for prev_model in previous_models:
                            prev_model_device = prev_model.to(self.device)
                            prev_model_device.eval()

                            with torch.no_grad():
                                # 获取历史模型的投影特征
                                _, prev_projection, _ = prev_model_device(data)

                            # 将历史特征存储到滑动窗口中
                            # 使用全局平均池化来获得固定大小的特征表示
                            pooled_prev_projection = torch.mean(prev_projection, dim=0, keepdim=True)  # (1, feature_dim)
                            Z_prev[cnt % C] = pooled_prev_projection.detach()

                            # 遍历滑动窗口中的历史特征
                            for i in range(C):
                                if Z_prev[i] is not None:
                                    # 对当前投影也进行全局平均池化
                                    pooled_current_projection = torch.mean(projection, dim=0, keepdim=True)  # (1, feature_dim)

                                    # 计算当前特征与历史特征的相似度
                                    t = torch.mean(cos(pooled_current_projection, Z_prev[i]).reshape(-1, 1))
                                    # if t >= 0.5:
                                    #     # 相似度高，作为正样本
                                    #     posi = posi + torch.exp(t / temperature)
                                    # else:
                                    #     # 相似度低，作为负样本
                                    #     nega = nega + torch.exp(t / temperature)
                                    # 使用软阈值而非硬阈值，添加数值稳定性检查
                                    t_clamped = torch.clamp(t, min=-1.0, max=1.0)  # 限制相似度范围
                                    similarity_weight = torch.sigmoid((t_clamped - 0.5) * 10)  # sigmoid函数使过渡更平滑

                                    # 添加数值稳定性检查
                                    exp_t = torch.clamp(torch.exp(t_clamped / temperature), min=1e-8, max=1e8)
                                    posi = posi + similarity_weight * exp_t
                                    nega = nega + (1 - similarity_weight) * exp_t

                            # 将历史模型移回CPU以节省GPU内存
                            prev_model_device.to('cpu')
                            del prev_model_device  # 显式删除引用
                            cnt += 1

                        # 计算对比学习损失（完全遵循SWIM原始实现）
                        total_sim = posi + nega
                        if total_sim > 1e-8:  # 添加数值稳定性检查
                            ratio = torch.clamp(posi / total_sim, min=1e-8, max=1.0-1e-8)
                            loss_contrastive = torch.mean(-torch.log(ratio))
                        else:
                            loss_contrastive = torch.tensor(0.0, device=self.device)

                    # 动态调整损失权重（SWIM算法的核心 - 适配异步联邦学习）
                    dynamic_mu = self._calculate_async_dynamic_weight(
                        async_weight_strategy, epoch, epochs, round_num, total_rounds
                    )

                    # 总损失
                    total_loss = (1 - dynamic_mu) * loss_classification + dynamic_mu * loss_contrastive

                    # 反向传播
                    total_loss.backward()

                    # # 添加梯度裁剪以防止梯度爆炸
                    # torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    #
                    # # 添加学习率预热和衰减
                    # if round_num < 10:
                    #     # 前10轮预热
                    #     actual_lr = lr * (round_num + 1) / 10
                    # else:
                    #     # 之后余弦衰减
                    #     decay_factor = 0.5 * (1 + np.cos(np.pi * (round_num - 10) / (total_rounds - 10)))
                    #     actual_lr = lr * max(0.1, decay_factor)
                    #
                    # # 更新优化器学习率
                    # for param_group in optimizer.param_groups:
                    #     param_group['lr'] = actual_lr

                    # 更新参数
                    optimizer.step()

            # 更新本地训练轮次计数器
            self.local_training_rounds += epochs

            # 将全局模型移回CPU
            if global_model is not None:
                global_model.to('cpu')

            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 返回训练后的模型参数
            return model.state_dict()

        except Exception as e:
            print(f"客户端 {self.id} SWIM训练出错: {e}")
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            # 返回原始参数
            return global_parameters

    def _calculate_async_dynamic_weight(self, strategy, current_epoch, total_epochs,
                                       round_num, total_rounds):
        """
        计算适配异步联邦学习的动态权重

        Args:
            strategy: 权重计算策略
            current_epoch: 当前epoch
            total_epochs: 总epoch数
            round_num: 全局轮次（可能过时）
            total_rounds: 总轮次

        Returns:
            dynamic_mu: 动态权重值
        """
        if strategy == 'local_rounds':
            #策略1：基于本地训练轮次的权重计算
            #使用客户端自己的训练进度，不依赖全局轮次
            local_progress = self.local_training_rounds / (total_rounds * total_epochs)
            local_progress = min(1.0, local_progress)  # 防止超过1
            dynamic_mu = 0.5 - local_progress / 2
            # local_progress = self.local_training_rounds / (total_rounds * total_epochs)
            # local_progress = min(1.0, local_progress)
            # dynamic_mu = 0.5 * np.exp(-2 * local_progress)

        elif strategy == 'global_progress':
            # 策略2：基于全局进度估计的权重计算
            # 使用传入的全局轮次，但考虑可能的延迟
            estimated_progress = (round_num + 1) / total_rounds
            # 添加延迟容忍度，避免权重突变
            dynamic_mu = 0.5 - estimated_progress / 2

        elif strategy == 'fixed_schedule':
            # 策略3：固定权重调度
            # 基于本地epoch进度的固定调度
            epoch_progress = current_epoch / total_epochs
            dynamic_mu = 0.5 - epoch_progress / 2

        else:
            # 默认：原始SWIM方法（可能不适合异步环境）
            dynamic_mu = 0.5 - (round_num + 1) / (2 * total_rounds)

        # 确保权重在合理范围内
        #dynamic_mu = max(0.0, min(1.0, dynamic_mu))
        dynamic_mu = max(0.1, min(0.6, dynamic_mu))
        return dynamic_mu


class ClientsGroup(object):
    """
    客户端组管理类
    负责创建和管理多个联邦学习客户端，使用SWIM的数据分配策略
    """
    def __init__(self, dataset, datadir, partition, n_clients, beta, device,
                 train_batch_size=64, test_batch_size=100):
        """
        初始化客户端组
        Args:
            dataset: 数据集名称（'cifar10'或'cifar100'）
            datadir: 数据目录
            partition: 数据分割策略（'iid'或'noniid'）
            n_clients: 客户端数量
            beta: 非IID数据分布参数
            device: 计算设备
            train_batch_size: 训练批次大小
            test_batch_size: 测试批次大小
        """
        self.dataset = dataset
        self.datadir = datadir
        self.partition = partition
        self.n_clients = n_clients
        self.beta = beta
        self.device = device
        self.train_batch_size = train_batch_size
        self.test_batch_size = test_batch_size
        self.clients = {}

        # 执行数据集分配
        self._setup_clients()

    def _setup_clients(self):
        """
        设置客户端数据分配（使用快速版本）
        """
        # 使用原始datasets模块（不使用fast_datasets）
        from datasets import partition_data, get_dataloader
        print("📊 使用标准数据划分算法")

        # 使用标准的数据分割方法
        X_train, y_train, X_test, y_test, net_dataidx_map, traindata_cls_counts = partition_data(
            self.dataset, self.datadir, './logs', self.partition, self.n_clients, beta=self.beta)
        
        # 为每个客户端创建数据加载器
        for client_id in range(self.n_clients):
            # 获取客户端的数据索引
            dataidxs = net_dataidx_map[client_id]
            
            # 创建客户端的数据加载器（使用指定的批次大小）
            train_dl, test_dl, train_ds, test_ds = get_dataloader(
                self.dataset, self.datadir,
                train_bs=self.train_batch_size,
                test_bs=self.test_batch_size,
                dataidxs=dataidxs)
            
            # 创建客户端实例
            client = Client(train_dl, test_dl, self.device)
            self.clients[f'client{client_id}'] = client
        
        # 创建全局测试数据加载器（使用指定的批次大小）
        _, self.test_dataloader, _, _ = get_dataloader(
            self.dataset, self.datadir,
            train_bs=self.train_batch_size,
            test_bs=self.test_batch_size,
            dataidxs=None)
        
        print(f"✓ 成功创建 {self.n_clients} 个客户端")
        print(f"✓ 数据分布策略: {self.partition}")
        if self.partition == 'noniid':
            print(f"✓ Beta参数: {self.beta}")
