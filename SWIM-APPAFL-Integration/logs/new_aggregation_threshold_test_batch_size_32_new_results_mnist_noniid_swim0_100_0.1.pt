PK                    \  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/data.pklFB ZZ�}q (X   final_accuracyqG@X�
=p��X
   final_lossqG?��_��JX   accuracy_historyq]q(G@F�
=p��G@Mk��Q�G@U�Q�G@T�\(��G@W�33333G@W�\(�G@W�(�\G@W]p��
=G@V�33333G@W�33333G@W!��RG@W�Q�G@X\(�G@XVfffffG@X\�����G@W�     G@X�33333G@XZ=p��
G@XG
=p��G@Xmp��
=G@Xj�G�{G@X��G�{G@X��\)G@Xmp��
=G@Xj=p��
G@Xtz�G�G@X��\)G@X#33333G@X���
=qG@X���
=qG@X�p��
=G@X�\(�G@X��Q�G@X�\(�G@X�fffffG@XY�����G@X������G@X�\(�G@X���Q�G@X������G@X���
=qG@X��\)G@Xr�\(��G@X�z�G�G@X�=p��
G@X�\(�G@X�(�\G@X������G@X�z�G�G@X�fffffG@X�33333G@Xə����G@X���Q�G@X�\(�G@X��G�{G@X��\)G@X��Q�G@X�z�G�G@X�G�z�G@X�     G@X�z�G�G@X�\(�G@X�(�\G@X��\)G@X\(��G@X�Q��G@X��
=p�G@X\(��G@X�z�G�G@X�\(�G@X�33333G@X�z�G�G@X�\(�G@X�(�\G@X�fffffG@X�z�G�G@X�\(�G@X���RG@X�z�G�G@X�z�G�G@XθQ�G@X\(��G@X�z�G�G@X�\(�G@X�z�G�G@X�33333G@X���RG@X\(��G@X��G�{G@X�p��
=G@X\(��G@X�G�z�G@X���RG@X�fffffG@X˅�Q�G@XǮz�HG@X�     G@XУ�
=qG@XǮz�HG@X�
=p��eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   mnistqX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   resnetqX
   model_versionqX	   optimizedqX   use_swimqK X   out_dimqM X   temperatureq G?�      X   model_buffer_sizeq!KX   krq"G?ٙ�����X   async_weight_strategyq#X   local_roundsq$uu.PK�$�    PK                    c , new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/.format_versionFB( ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    f + new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/.storage_alignmentFB' ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    ] 3 new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/byteorderFB/ ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    [ 1 new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/versionFB- ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    j & new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/.data/serialization_idFB" ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400102849176489242810PK����(   (   PK          �$�    \                 new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/data.pklPK          ��܃      c             �  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/.format_versionPK          ?wq�      f             Q  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/.storage_alignmentPK          �=�      ]               new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/byteorderPK          ўgU      [             �  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/versionPK          ����(   (   j             �	  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim0_100_0.1/.data/serialization_idPK,       -                       [      x
      PK    �
         PK      [  x
    