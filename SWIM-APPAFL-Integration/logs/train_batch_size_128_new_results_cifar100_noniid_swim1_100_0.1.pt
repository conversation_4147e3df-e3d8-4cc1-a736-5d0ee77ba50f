PK                    G  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/data.pklFB ZZZZZZZZZZZZZZZZZZZZZZZ�}q (X   final_accuracyqG@:8Q��X
   final_lossqG@�%D�X   accuracy_historyq]q(G?�      G?�      G?�      G?�      G?�G�z�HG?�      G?�Q��RG?�������G?�\(��G?�      G@\(��G?�z�G�{G@��Q�G@ ��
=p�G@Q��RG?�=p��
=G@
=p��
G?�=p��
=G@�z�G�G?�ffffffG?�      G@
�G�z�G@�
=p��G@
�Q�G@\(�\G@p��
=qG@�G�z�G@(�\)G@!�
=p��G@��
=p�G@Q��RG?�Q��RG@%p��
=qG@$p��
=qG@�Q�G?�      G@������G?�G�z�HG@%k��Q�G@&�fffffG@#\(��G?�      G@)Q��RG@&��Q�G@&���RG@'�
=p��G@�\(�G@�Q�G@/#�
=p�G@1@     G@(�Q�G@)\(�G@1B�\(��G@������G@-z�G�{G@)aG�z�G@0��Q�G@1@     G@(z�G�G@2nz�G�G@5z�G�G@4c�
=p�G@ u\(�G@0�     G@,�fffffG@6�33333G@6B�\(��G@6(�\G@7�z�HG@70��
=qG@3Q��RG@6�     G@6�\(�G@8#�
=p�G@8�z�G�G@6E�Q�G@:�G�z�G@-Ǯz�HG@9��\)G@6Y�����G@5z�G�{G@7������G@9z�G�{G@5G�z�HG@8p��
=qG@;�Q�G@:nz�G�G@;(�\)G@:�p��
=G@6333333G@:�Q�G@;��\)G@=������G@=G�z�HG@<p��
=qG@:aG�z�G@<�     G@<���
=qG@;�\(�G@:8Q��eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   cifar100qX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�������X   train_batch_sizeqK@X   test_batch_sizeqK�X   modelqX   resnet50qX   use_swimqKX   out_dimqM X   temperatureqG?�      X   model_buffer_sizeqKX   krq G?ٙ�����X   async_weight_strategyq!X   local_roundsq"uu.PK����  �  PK                    N   train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/.format_versionFB ZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    Q @ train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/.storage_alignmentFB< ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    H  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/byteorderFB ZZZZlittlePK�=�      PK                    F  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/versionFB ZZ3
PKўgU      PK                    U ; train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/.data/serialization_idFB7 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400115767063550274279PKS�;(   (   PK          ����  �  G                 train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/data.pklPK          ��܃      N             t  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/.format_versionPK          ?wq�      Q               train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/.storage_alignmentPK          �=�      H             �  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/byteorderPK          ўgU      F             V  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/versionPK          S�;(   (   U             �  train_batch_size_128_new_results_cifar100_noniid_swim1_100_0.1/.data/serialization_idPK,       -                       �      �	      PK    �         PK      �  �	    