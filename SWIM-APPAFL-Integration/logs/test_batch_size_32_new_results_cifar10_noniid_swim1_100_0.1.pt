PK                    D  test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/data.pklFB ZZZZZZZZZZZZZZZZZZZZZZZZZZ�}q (X   final_accuracyqG@N/\(�X
   final_lossqG?�_���X   accuracy_historyq]q(G@$      G@$��
=p�G@6�=p��
G@4k��Q�G@,      G@0���RG@4�z�HG@;z�G�G@9�\(�G@7�     G@;�z�G�G@:Ǯz�HG@?O\(�G@;:�G�{G@=�=p��
G@@�     G@C@     G@>�Q��G@8�(�\G@>
=p��
G@9�G�z�G@@�(�\G@@9�����G@B�z�G�G@Dq��RG@B�33333G@DG�z�HG@CG�z�HG@G\(�\G@CZ�G�{G@Gb�\(��G@D�Q��G@FK��Q�G@D%�Q�G@C�z�G�G@JH�\)G@I\(�G@B���RG@C/\(�G@F:�G�{G@H�\(�G@I*=p��
G@L��
=p�G@HL�����G@I�z�G�G@F��Q�G@I      G@D�z�G�G@L�33333G@@"�\(��G@F#�
=p�G@J�\(��G@L�p��
=G@IQ��G@Io\(�G@F��
=p�G@I���RG@J333333G@JG�z�G@N�=p��
G@MP��
=qG@Iy�����G@Il�����G@L�z�G�G@NFfffffG@KE�Q�G@Lc�
=p�G@Iu\(�G@NO\(�G@Kp��
=qG@I��\(��G@M������G@LFfffffG@O�z�HG@D"�\(��G@L���
=qG@M��
=p�G@O,�����G@L
=p��G@Np��
=qG@I������G@JfffffG@K��Q�G@P�\(�G@JУ�
=qG@J������G@L������G@L�z�HG@MG�z�HG@P��RG@N������G@P��G�{G@O�G�z�G@PC33333G@N�Q�G@P��z�HG@K:�G�{G@P
�G�{G@P-p��
=G@N/\(�eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   cifar10qX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   resnetqX   use_swimqKX   out_dimqM X   temperatureqG?�      X   model_buffer_sizeqKX   krq G?ٙ�����X   async_weight_strategyq!X   local_roundsq"uu.PK0�RI�  �  PK                    K & test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/.format_versionFB" ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    N C test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/.storage_alignmentFB? ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    E  test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/byteorderFB ZZZZZZZlittlePK�=�      PK                    C 	 test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/versionFB ZZZZZ3
PKўgU      PK                    R > test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/.data/serialization_idFB: ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400070894257041210949PK]x�(   (   PK          0�RI�  �  D                 test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/data.pklPK          ��܃      K             q  test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/.format_versionPK          ?wq�      N               test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/.storage_alignmentPK          �=�      E             �  test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/byteorderPK          ўgU      C             V  test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/versionPK          ]x�(   (   R             �  test_batch_size_32_new_results_cifar10_noniid_swim1_100_0.1/.data/serialization_idPK,       -                       �      �	      PK    �         PK      �  �	    