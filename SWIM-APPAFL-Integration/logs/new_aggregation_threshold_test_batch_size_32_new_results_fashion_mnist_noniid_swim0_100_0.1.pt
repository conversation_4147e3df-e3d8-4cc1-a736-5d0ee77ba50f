PK                    d > new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/data.pklFB: ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ�}q (X   final_accuracyqG@U������X
   final_lossqG?֍���6�X   accuracy_historyq]q(G@>�z�G�G@75\(�G@BQ��G@N�\(�G@P�G�z�G@QvfffffG@Lp��
=qG@Ol�����G@Rҏ\(��G@R+��Q�G@N,�����G@S4z�G�G@Rtz�G�G@SDz�G�G@Q ��
=qG@R�\(�G@R�     G@Q�\(��G@R�Q��G@T!G�z�G@T     G@S�G�z�G@S�     G@T$z�G�G@S�33333G@T(Q��G@Qu�Q�G@S}p��
=G@S��z�HG@T\(�G@S��
=p�G@Tp��
=qG@SHQ��G@T������G@TxQ��G@TY�����G@S���RG@STz�G�G@U
p��
=G@S��Q�G@U!��RG@T������G@T�z�G�G@T�33333G@T�z�HG@T.�Q�G@T�\(�G@S�Q�G@S�=p��
G@T�fffffG@U<�����G@Tҏ\(��G@T��z�HG@UL(�\G@Tc33333G@U\(�G@U\(�G@UAG�z�G@Uc�
=p�G@Tٙ����G@S��\(��G@U�G�{G@U�\(��G@TJ�G�{G@T*=p��
G@T��Q�G@T��G�{G@U��Q�G@T���RG@Ua��RG@UDz�G�G@U>�Q�G@Up��
=qG@T�Q��G@Uy�����G@U���
=qG@Ue\(�G@T�33333G@T�z�G�G@U��Q�G@UW
=p��G@UG�z�HG@U�     G@R�G�z�G@T=p��
=G@UY�����G@T������G@U2�\(��G@U
=p��
G@T�G�z�G@T�
=p��G@Ue�Q�G@U]p��
=G@TУ�
=qG@Ul�����G@U%\(�G@Uҏ\(��G@VQ��G@V�����G@U������eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX
   fashion_mnistqX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   cnnqX
   model_versionqX	   optimizedqX   use_swimqK X   out_dimqM X   temperatureq G?�      X   model_buffer_sizeq!KX   krq"G?ٙ�����X   async_weight_strategyq#X   local_roundsq$uu.PK�Y)    PK                    k  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/.format_versionFB ZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    n # new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/.storage_alignmentFB ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    e + new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/byteorderFB' ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    c ) new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/versionFB% ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    r  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/.data/serialization_idFB ZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400061674546459504593PK��E�(   (   PK          �Y)    d                 new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/data.pklPK          ��܃      k             �  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/.format_versionPK          ?wq�      n             �  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/.storage_alignmentPK          �=�      e             R  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/byteorderPK          ўgU      c             	  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/versionPK          ��E�(   (   r             �	  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim0_100_0.1/.data/serialization_idPK,       -                       �      �
      PK    C         PK      �  �
    