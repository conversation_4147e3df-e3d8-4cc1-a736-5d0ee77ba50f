PK                    d > new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/data.pklFB: ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ�}q (X   final_accuracyqG@U�����X
   final_lossqG?�Pт2��X   accuracy_historyq]q(G@8��Q�G@%W
=p��G@B"�\(��G@Mz�G�{G@N��Q�G@Q�z�HG@L�\(��G@MJ=p��
G@R������G@QTz�G�G@L%�Q�G@RC�
=p�G@Q?\(�G@R�z�HG@PR�\(��G@R(�\)G@P������G@P��
=p�G@QO\(�G@Sp��
=qG@SffffffG@R������G@R�G�z�G@S��Q�G@R��G�{G@Su�Q�G@P�     G@Rh�\)G@S�Q�G@R�z�G�G@R��Q�G@S5\(�G@R�\(�G@S������G@SL(�\G@S��z�HG@R�\(�G@Ru�Q�G@T�z�G�G@S@     G@T&fffffG@S�=p��
G@S��Q�G@S�z�G�G@S�Q�G@R��z�HG@Sp��
=qG@R0     G@R�Q��G@Sw�z�HG@TC33333G@S�G�z�G@S޸Q�G@T��\)G@SY�����G@Tz�G�G@T�Q�G@Tc33333G@T^z�G�G@S��G�{G@Q��z�HG@S��G�{G@Rҏ\(��G@R���
=qG@S*�G�{G@S���
=qG@T%\(�G@T�G�z�G@Sp     G@T[��Q�G@T�=p��
G@T"�\(��G@T���
=qG@Sٙ����G@TA��RG@T�p��
=G@TO\(�G@S]p��
=G@S|(�\G@T�\(�G@S�z�G�G@TQ��G@T��
=p�G@P�
=p��G@S��\(��G@S�\(��G@S��z�HG@S��
=p�G@S�fffffG@Sw
=p��G@Sg�z�HG@T�\(�G@Tr�\(��G@T0     G@T�fffffG@T>z�G�G@U!��RG@T������G@Un�Q�G@U�����eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX
   fashion_mnistqX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   cnnqX
   model_versionqX	   optimizedqX   use_swimqKX   out_dimqM X   temperatureq G?�      X   model_buffer_sizeq!KX   krq"G?ٙ�����X   async_weight_strategyq#X   local_roundsq$uu.PK:���    PK                    k  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/.format_versionFB ZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    n # new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/.storage_alignmentFB ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    e + new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/byteorderFB' ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    c ) new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/versionFB% ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    r  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/.data/serialization_idFB ZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400109377215908880477PKԿ�(   (   PK          :���    d                 new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/data.pklPK          ��܃      k             �  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/.format_versionPK          ?wq�      n             �  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/.storage_alignmentPK          �=�      e             R  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/byteorderPK          ўgU      c             	  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/versionPK          Կ�(   (   r             �	  new_aggregation_threshold_test_batch_size_32_new_results_fashion_mnist_noniid_swim1_100_0.1/.data/serialization_idPK,       -                       �      �
      PK    C         PK      �  �
    