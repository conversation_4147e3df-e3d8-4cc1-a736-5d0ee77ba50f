PK                    ^  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/data.pklFB  �}q (X   final_accuracyqG@L�(�\X
   final_lossqG?���tX   accuracy_historyq]q(G@$      G@$      G@.L�����G@$aG�z�G@%�\(�G@(
=p��
G@0������G@8+��Q�G@2�     G@6�fffffG@8W
=p��G@4�=p��
G@:#�
=p�G@4��RG@=#�
=p�G@:G�z�HG@>�(�\G@9�z�G�G@2xQ��G@;(�\)G@-�33333G@?���Q�G@9���RG@@���
=qG@=�
=p��G@?�z�G�G@@�     G@@�p��
=G@A�Q��G@BG�z�G@Gz�G�{G@A'�z�HG@B~�Q�G@?z�G�{G@=J=p��
G@D�fffffG@Ey�����G@8��
=p�G@?O\(�G@B"�\(��G@F˅�Q�G@D������G@IǮz�HG@E�=p��
G@C���
=qG@C&fffffG@D��G�{G@@��Q�G@I�\(�G@3��z�HG@Ae�Q�G@He�Q�G@G���RG@E"�\(��G@F4z�G�G@Cz�G�{G@Eu\(�G@F�33333G@FU\(�G@M��Q�G@K��G�{G@Gy�����G@E�fffffG@G�Q�G@K�G�z�G@G�p��
=G@Gٙ����G@I,�����G@L/\(�G@F�z�G�G@F���RG@L1��RG@LL�����G@L�fffffG@C\(�\G@IP��
=qG@KAG�z�G@H޸Q�G@H�p��
=G@L      G@Ep��
=G@I5\(�G@I(�\)G@O�p��
=G@K������G@JQ��G@J��\(��G@K0��
=qG@KG�z�G@LY�����G@KaG�z�G@Mtz�G�G@M�
=p��G@K33333G@K�=p��
G@N}p��
=G@J\(�G@NfffffG@M���
=qG@L�(�\eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   cifar10qX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   resnetqX
   model_versionqX	   optimizedqX   use_swimqK X   out_dimqM X   temperatureq G?�      X   model_buffer_sizeq!KX   krq"G?ٙ�����X   async_weight_strategyq#X   local_roundsq$uu.PK*�3f    PK                    e ( new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.format_versionFB$ ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    h ) new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.storage_alignmentFB% ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    _ 1 new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/byteorderFB- ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    ] / new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/versionFB+ ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    l $ new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.data/serialization_idFB  ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400075811348184963953PK��(   (   PK          *�3f    ^                 new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/data.pklPK          ��܃      e             �  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.format_versionPK          ?wq�      h             Q  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.storage_alignmentPK          �=�      _               new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/byteorderPK          ўgU      ]             �  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/versionPK          ��(   (   l             �	  new_aggregation_threshold_test_batch_size_32_new_results_cifar10_noniid_swim0_100_0.1/.data/serialization_idPK,       -                       g      x
      PK    �
         PK      g  x
    