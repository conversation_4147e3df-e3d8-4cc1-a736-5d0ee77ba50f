# SWIM-APPAFL改进效果测试脚本
# 运行改进前后的对比实验，验证稳定性提升效果
import subprocess
import os
import time
import json
import torch
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime


class ImprovementTester:
    """
    改进效果测试器
    对比改进前后的训练稳定性和性能
    """
    
    def __init__(self, log_dir='./test_logs'):
        """
        初始化测试器
        Args:
            log_dir: 测试日志目录
        """
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        self.test_results = {}
        
        print(f"✓ 改进效果测试器已初始化，日志目录: {log_dir}")

    def run_quick_test(self, use_improvements=True):
        """
        运行快速测试
        Args:
            use_improvements: 是否使用改进版本
        Returns:
            dict: 测试结果
        """
        print(f"\n{'='*60}")
        print(f"运行{'改进版本' if use_improvements else '原始版本'}快速测试")
        print(f"{'='*60}")
        
        # 构建测试命令
        cmd = [
            'python', 'main.py',
            '--dataset', 'cifar10',
            '--n_clients', '10',
            '--cfraction', '0.5',
            '--local_epochs', '3',
            '--comm_rounds', '20',
            '--use_swim', '1',
            '--model', 'resnet',
            '--model_version', 'optimized' if use_improvements else 'swim_original',
            '--logdir', self.log_dir,
            '--seed', '42'
        ]
        
        if use_improvements:
            # 使用改进的参数
            cmd.extend([
                '--train_batch_size', '32',
                '--temperature', '0.7',
                '--out_dim', '128',
                '--kr', '0.3',
                '--async_weight_strategy', 'fixed_schedule'
            ])
        else:
            # 使用原始参数
            cmd.extend([
                '--train_batch_size', '64',
                '--temperature', '0.5',
                '--out_dim', '256',
                '--kr', '0.4',
                '--async_weight_strategy', 'local_rounds'
            ])
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30分钟超时
            end_time = time.time()
            
            test_result = {
                'success': result.returncode == 0,
                'duration': end_time - start_time,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'version': 'improved' if use_improvements else 'original'
            }
            
            if result.returncode == 0:
                print(f"✓ 测试成功完成，耗时: {test_result['duration']:.2f}秒")
                # 尝试解析结果
                test_result.update(self._parse_output(result.stdout))
            else:
                print(f"✗ 测试失败")
                print(f"错误输出: {result.stderr}")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"✗ 测试超时（超过30分钟）")
            return {'success': False, 'error': 'timeout', 'version': 'improved' if use_improvements else 'original'}
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            return {'success': False, 'error': str(e), 'version': 'improved' if use_improvements else 'original'}

    def _parse_output(self, stdout):
        """
        解析训练输出，提取关键指标
        Args:
            stdout: 标准输出
        Returns:
            dict: 解析的指标
        """
        metrics = {}
        
        # 提取最终准确率
        lines = stdout.split('\n')
        for line in lines:
            if '最终准确率:' in line:
                try:
                    accuracy = float(line.split('最终准确率:')[1].split('%')[0].strip())
                    metrics['final_accuracy'] = accuracy
                except:
                    pass
            elif '最终损失:' in line:
                try:
                    loss = float(line.split('最终损失:')[1].strip())
                    metrics['final_loss'] = loss
                except:
                    pass
            elif '总训练时间:' in line:
                try:
                    time_str = line.split('总训练时间:')[1].split('小时')[0].strip()
                    training_time = float(time_str)
                    metrics['training_time_hours'] = training_time
                except:
                    pass
        
        # 提取准确率历史（用于稳定性分析）
        accuracy_history = []
        for line in lines:
            if '准确率:' in line and '%' in line:
                try:
                    acc_str = line.split('准确率:')[1].split('%')[0].strip()
                    accuracy_history.append(float(acc_str))
                except:
                    pass
        
        if accuracy_history:
            metrics['accuracy_history'] = accuracy_history
            metrics['accuracy_variance'] = np.var(accuracy_history)
            metrics['accuracy_trend'] = self._calculate_trend(accuracy_history)
        
        return metrics

    def _calculate_trend(self, values):
        """
        计算数值序列的趋势
        Args:
            values: 数值列表
        Returns:
            float: 趋势斜率
        """
        if len(values) < 2:
            return 0
        
        x = np.arange(len(values))
        slope, _ = np.polyfit(x, values, 1)
        return slope

    def run_comparison_test(self):
        """
        运行对比测试
        Returns:
            dict: 对比结果
        """
        print("\n" + "="*80)
        print("SWIM-APPAFL改进效果对比测试")
        print("="*80)
        
        # 运行原始版本测试
        print("\n1. 运行原始版本测试...")
        original_result = self.run_quick_test(use_improvements=False)
        
        # 运行改进版本测试
        print("\n2. 运行改进版本测试...")
        improved_result = self.run_quick_test(use_improvements=True)
        
        # 对比分析
        comparison = self._analyze_comparison(original_result, improved_result)
        
        # 保存结果
        self._save_comparison_results(original_result, improved_result, comparison)
        
        return comparison

    def _analyze_comparison(self, original, improved):
        """
        分析对比结果
        Args:
            original: 原始版本结果
            improved: 改进版本结果
        Returns:
            dict: 对比分析
        """
        comparison = {
            'timestamp': datetime.now().isoformat(),
            'original_success': original.get('success', False),
            'improved_success': improved.get('success', False)
        }
        
        if original.get('success') and improved.get('success'):
            # 性能对比
            orig_acc = original.get('final_accuracy', 0)
            impr_acc = improved.get('final_accuracy', 0)
            comparison['accuracy_improvement'] = impr_acc - orig_acc
            comparison['accuracy_improvement_percent'] = (impr_acc - orig_acc) / orig_acc * 100 if orig_acc > 0 else 0
            
            orig_loss = original.get('final_loss', float('inf'))
            impr_loss = improved.get('final_loss', float('inf'))
            comparison['loss_improvement'] = orig_loss - impr_loss
            comparison['loss_improvement_percent'] = (orig_loss - impr_loss) / orig_loss * 100 if orig_loss > 0 else 0
            
            # 稳定性对比
            orig_var = original.get('accuracy_variance', 0)
            impr_var = improved.get('accuracy_variance', 0)
            comparison['stability_improvement'] = orig_var - impr_var
            comparison['stability_improvement_percent'] = (orig_var - impr_var) / orig_var * 100 if orig_var > 0 else 0
            
            # 收敛速度对比
            orig_trend = original.get('accuracy_trend', 0)
            impr_trend = improved.get('accuracy_trend', 0)
            comparison['convergence_improvement'] = impr_trend - orig_trend
            
            # 训练时间对比
            orig_time = original.get('duration', 0)
            impr_time = improved.get('duration', 0)
            comparison['time_change'] = impr_time - orig_time
            comparison['time_change_percent'] = (impr_time - orig_time) / orig_time * 100 if orig_time > 0 else 0
            
        return comparison

    def _save_comparison_results(self, original, improved, comparison):
        """
        保存对比结果
        Args:
            original: 原始版本结果
            improved: 改进版本结果
            comparison: 对比分析
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results = {
            'original': original,
            'improved': improved,
            'comparison': comparison
        }
        
        result_file = os.path.join(self.log_dir, f"comparison_results_{timestamp}.json")
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n✓ 对比结果已保存到: {result_file}")

    def print_comparison_summary(self, comparison):
        """
        打印对比总结
        Args:
            comparison: 对比分析结果
        """
        print("\n" + "="*80)
        print("SWIM-APPAFL改进效果总结")
        print("="*80)
        
        if comparison.get('original_success') and comparison.get('improved_success'):
            print("✓ 两个版本都成功完成测试")
            
            # 准确率改进
            acc_impr = comparison.get('accuracy_improvement', 0)
            acc_impr_pct = comparison.get('accuracy_improvement_percent', 0)
            print(f"\n📈 准确率改进:")
            print(f"   绝对改进: {acc_impr:+.2f}%")
            print(f"   相对改进: {acc_impr_pct:+.2f}%")
            
            # 损失改进
            loss_impr = comparison.get('loss_improvement', 0)
            loss_impr_pct = comparison.get('loss_improvement_percent', 0)
            print(f"\n📉 损失改进:")
            print(f"   绝对改进: {loss_impr:+.4f}")
            print(f"   相对改进: {loss_impr_pct:+.2f}%")
            
            # 稳定性改进
            stab_impr = comparison.get('stability_improvement', 0)
            stab_impr_pct = comparison.get('stability_improvement_percent', 0)
            print(f"\n🎯 稳定性改进:")
            print(f"   方差减少: {stab_impr:+.4f}")
            print(f"   相对改进: {stab_impr_pct:+.2f}%")
            
            # 收敛速度改进
            conv_impr = comparison.get('convergence_improvement', 0)
            print(f"\n⚡ 收敛速度改进:")
            print(f"   趋势改进: {conv_impr:+.4f}")
            
            # 训练时间变化
            time_change = comparison.get('time_change', 0)
            time_change_pct = comparison.get('time_change_percent', 0)
            print(f"\n⏱️  训练时间变化:")
            print(f"   时间变化: {time_change:+.2f}秒")
            print(f"   相对变化: {time_change_pct:+.2f}%")
            
            # 总体评估
            print(f"\n🏆 总体评估:")
            improvements = 0
            if acc_impr > 0:
                print("   ✓ 准确率有所提升")
                improvements += 1
            if loss_impr > 0:
                print("   ✓ 损失有所降低")
                improvements += 1
            if stab_impr > 0:
                print("   ✓ 训练稳定性有所改善")
                improvements += 1
            if conv_impr > 0:
                print("   ✓ 收敛速度有所提升")
                improvements += 1
            
            if improvements >= 3:
                print("   🎉 改进效果显著！")
            elif improvements >= 2:
                print("   👍 改进效果良好")
            elif improvements >= 1:
                print("   📈 有一定改进")
            else:
                print("   ⚠️  改进效果不明显，需要进一步调优")
                
        else:
            print("❌ 测试未能成功完成，无法进行对比分析")
            if not comparison.get('original_success'):
                print("   原始版本测试失败")
            if not comparison.get('improved_success'):
                print("   改进版本测试失败")
        
        print("="*80)


def main():
    """主函数"""
    print("SWIM-APPAFL改进效果测试")
    print("此脚本将运行改进前后的对比实验")
    
    # 创建测试器
    tester = ImprovementTester()
    
    # 运行对比测试
    comparison = tester.run_comparison_test()
    
    # 打印总结
    tester.print_comparison_summary(comparison)


if __name__ == '__main__':
    main()
