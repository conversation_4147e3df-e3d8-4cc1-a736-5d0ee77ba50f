# SWIM-APPAFL训练监控和调试工具
# 提供训练过程中的详细监控和可视化功能
import torch
import numpy as np
import matplotlib.pyplot as plt
import time
import os
from collections import defaultdict, deque
import json


class TrainingMonitor:
    """
    训练监控器
    监控训练过程中的各种指标，包括损失、梯度、GPU内存等
    """
    
    def __init__(self, log_dir='./logs', save_interval=10):
        """
        初始化训练监控器
        Args:
            log_dir: 日志保存目录
            save_interval: 保存间隔（轮次）
        """
        self.log_dir = log_dir
        self.save_interval = save_interval
        os.makedirs(log_dir, exist_ok=True)
        
        # 监控数据存储
        self.metrics = defaultdict(list)
        self.round_metrics = defaultdict(list)
        self.client_metrics = defaultdict(lambda: defaultdict(list))
        
        # 实时监控窗口（最近N个数据点）
        self.window_size = 50
        self.recent_losses = deque(maxlen=self.window_size)
        self.recent_accuracies = deque(maxlen=self.window_size)
        
        # 时间记录
        self.start_time = time.time()
        self.round_start_time = None
        
        print(f"✓ 训练监控器已初始化，日志目录: {log_dir}")

    def start_round(self, round_num):
        """开始新的训练轮次"""
        self.round_start_time = time.time()
        self.current_round = round_num
        
        # GPU内存监控
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gpu_memory = torch.cuda.memory_allocated() / 1024**3
            gpu_cached = torch.cuda.memory_reserved() / 1024**3
            self.round_metrics['gpu_memory_allocated'].append(gpu_memory)
            self.round_metrics['gpu_memory_cached'].append(gpu_cached)

    def log_client_training(self, client_id, epoch, batch_idx, 
                           loss_classification, loss_contrastive=None, 
                           dynamic_mu=None, grad_norm=None):
        """
        记录客户端训练过程中的指标
        Args:
            client_id: 客户端ID
            epoch: 当前epoch
            batch_idx: 当前batch索引
            loss_classification: 分类损失
            loss_contrastive: 对比学习损失（可选）
            dynamic_mu: 动态权重（可选）
            grad_norm: 梯度范数（可选）
        """
        # 记录客户端级别的指标
        self.client_metrics[client_id]['loss_classification'].append(loss_classification)
        
        if loss_contrastive is not None:
            self.client_metrics[client_id]['loss_contrastive'].append(loss_contrastive)
        
        if dynamic_mu is not None:
            self.client_metrics[client_id]['dynamic_mu'].append(dynamic_mu)
            
        if grad_norm is not None:
            self.client_metrics[client_id]['grad_norm'].append(grad_norm)

    def log_round_results(self, round_num, accuracy, loss, 
                         participating_clients, stale_clients=None):
        """
        记录每轮的结果
        Args:
            round_num: 轮次编号
            accuracy: 全局准确率
            loss: 全局损失
            participating_clients: 参与训练的客户端数量
            stale_clients: 使用陈旧模型的客户端数量
        """
        # 记录基本指标
        self.metrics['round'].append(round_num)
        self.metrics['accuracy'].append(accuracy)
        self.metrics['loss'].append(loss)
        self.metrics['participating_clients'].append(participating_clients)
        
        if stale_clients is not None:
            self.metrics['stale_clients'].append(stale_clients)
        
        # 更新实时监控窗口
        self.recent_losses.append(loss)
        self.recent_accuracies.append(accuracy)
        
        # 计算轮次耗时
        if self.round_start_time is not None:
            round_duration = time.time() - self.round_start_time
            self.metrics['round_duration'].append(round_duration)
        
        # GPU内存监控
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.memory_allocated() / 1024**3
            gpu_cached = torch.cuda.memory_reserved() / 1024**3
            self.metrics['gpu_memory'].append(gpu_memory)
            self.metrics['gpu_cached'].append(gpu_cached)

    def log_model_stats(self, model, prefix="global"):
        """
        记录模型参数统计信息
        Args:
            model: 要分析的模型
            prefix: 前缀标识（如"global", "client_X"等）
        """
        total_params = 0
        total_grad_norm = 0
        layer_stats = {}
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                # 参数统计
                param_count = param.numel()
                total_params += param_count
                
                # 参数值统计
                param_mean = param.data.mean().item()
                param_std = param.data.std().item()
                param_max = param.data.max().item()
                param_min = param.data.min().item()
                
                layer_stats[name] = {
                    'param_count': param_count,
                    'mean': param_mean,
                    'std': param_std,
                    'max': param_max,
                    'min': param_min
                }
                
                # 梯度统计（如果存在）
                if param.grad is not None:
                    grad_norm = param.grad.norm().item()
                    total_grad_norm += grad_norm ** 2
                    layer_stats[name]['grad_norm'] = grad_norm
        
        # 记录总体统计
        self.metrics[f'{prefix}_total_params'].append(total_params)
        self.metrics[f'{prefix}_total_grad_norm'].append(np.sqrt(total_grad_norm))
        
        return layer_stats

    def check_training_stability(self):
        """
        检查训练稳定性
        Returns:
            dict: 稳定性分析结果
        """
        stability_report = {}
        
        # 检查损失趋势
        if len(self.recent_losses) >= 10:
            recent_losses = list(self.recent_losses)
            loss_trend = np.polyfit(range(len(recent_losses)), recent_losses, 1)[0]
            loss_variance = np.var(recent_losses)
            
            stability_report['loss_trend'] = loss_trend
            stability_report['loss_variance'] = loss_variance
            stability_report['loss_stable'] = abs(loss_trend) < 0.01 and loss_variance < 1.0
        
        # 检查准确率趋势
        if len(self.recent_accuracies) >= 10:
            recent_accs = list(self.recent_accuracies)
            acc_trend = np.polyfit(range(len(recent_accs)), recent_accs, 1)[0]
            acc_variance = np.var(recent_accs)
            
            stability_report['accuracy_trend'] = acc_trend
            stability_report['accuracy_variance'] = acc_variance
            stability_report['accuracy_stable'] = acc_trend > -0.1 and acc_variance < 100
        
        # 检查GPU内存使用
        if torch.cuda.is_available() and len(self.metrics['gpu_memory']) > 0:
            recent_gpu = self.metrics['gpu_memory'][-10:]
            gpu_trend = np.polyfit(range(len(recent_gpu)), recent_gpu, 1)[0]
            stability_report['gpu_memory_trend'] = gpu_trend
            stability_report['gpu_memory_stable'] = gpu_trend < 0.1  # 内存增长不超过0.1GB/轮
        
        return stability_report

    def save_metrics(self, filename=None):
        """
        保存监控指标到文件
        Args:
            filename: 保存文件名（可选）
        """
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"training_metrics_{timestamp}.json"
        
        filepath = os.path.join(self.log_dir, filename)
        
        # 准备保存数据
        save_data = {
            'metrics': dict(self.metrics),
            'round_metrics': dict(self.round_metrics),
            'client_metrics': {k: dict(v) for k, v in self.client_metrics.items()},
            'total_training_time': time.time() - self.start_time
        }
        
        # 保存为JSON
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 训练指标已保存到: {filepath}")

    def plot_training_curves(self, save_plot=True):
        """
        绘制训练曲线
        Args:
            save_plot: 是否保存图片
        """
        if len(self.metrics['accuracy']) == 0:
            print("警告：没有足够的数据绘制训练曲线")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('SWIM-APPAFL训练监控', fontsize=16)
        
        # 准确率曲线
        axes[0, 0].plot(self.metrics['round'], self.metrics['accuracy'], 'b-', linewidth=2)
        axes[0, 0].set_title('全局准确率')
        axes[0, 0].set_xlabel('训练轮次')
        axes[0, 0].set_ylabel('准确率 (%)')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 损失曲线
        axes[0, 1].plot(self.metrics['round'], self.metrics['loss'], 'r-', linewidth=2)
        axes[0, 1].set_title('全局损失')
        axes[0, 1].set_xlabel('训练轮次')
        axes[0, 1].set_ylabel('损失值')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 参与客户端数量
        axes[1, 0].plot(self.metrics['round'], self.metrics['participating_clients'], 'g-', linewidth=2)
        if 'stale_clients' in self.metrics:
            axes[1, 0].plot(self.metrics['round'], self.metrics['stale_clients'], 'orange', linewidth=2, linestyle='--')
            axes[1, 0].legend(['参与训练', '陈旧模型'])
        axes[1, 0].set_title('客户端参与情况')
        axes[1, 0].set_xlabel('训练轮次')
        axes[1, 0].set_ylabel('客户端数量')
        axes[1, 0].grid(True, alpha=0.3)
        
        # GPU内存使用
        if 'gpu_memory' in self.metrics and len(self.metrics['gpu_memory']) > 0:
            axes[1, 1].plot(self.metrics['round'], self.metrics['gpu_memory'], 'purple', linewidth=2)
            axes[1, 1].plot(self.metrics['round'], self.metrics['gpu_cached'], 'purple', linewidth=2, linestyle='--')
            axes[1, 1].legend(['已分配', '已缓存'])
            axes[1, 1].set_title('GPU内存使用')
            axes[1, 1].set_xlabel('训练轮次')
            axes[1, 1].set_ylabel('内存 (GB)')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, 'GPU监控不可用', ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('GPU内存使用')
        
        plt.tight_layout()
        
        if save_plot:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            plot_path = os.path.join(self.log_dir, f"training_curves_{timestamp}.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"✓ 训练曲线已保存到: {plot_path}")
        
        plt.show()

    def print_summary(self):
        """打印训练总结"""
        if len(self.metrics['accuracy']) == 0:
            print("没有训练数据可供总结")
            return
        
        print("\n" + "="*60)
        print("SWIM-APPAFL训练总结")
        print("="*60)
        
        # 基本统计
        final_accuracy = self.metrics['accuracy'][-1]
        max_accuracy = max(self.metrics['accuracy'])
        final_loss = self.metrics['loss'][-1]
        min_loss = min(self.metrics['loss'])
        
        print(f"最终准确率: {final_accuracy:.2f}%")
        print(f"最高准确率: {max_accuracy:.2f}%")
        print(f"最终损失: {final_loss:.4f}")
        print(f"最低损失: {min_loss:.4f}")
        
        # 训练时间
        total_time = time.time() - self.start_time
        avg_round_time = np.mean(self.metrics['round_duration']) if 'round_duration' in self.metrics else 0
        
        print(f"总训练时间: {total_time/3600:.2f} 小时")
        print(f"平均每轮时间: {avg_round_time:.2f} 秒")
        
        # 稳定性分析
        stability = self.check_training_stability()
        print(f"\n稳定性分析:")
        for key, value in stability.items():
            print(f"  {key}: {value}")
        
        print("="*60)
